# 阿里云OSS上传签名问题修复总结

## 问题描述

根据阿里云OSS错误诊断，POST请求表单中签名表单域为空，具体表现为：
- 对于默认的V1版签名，需要通过`Signature`表单域提供签名信息
- 请求中缺少`Signature`表单域或该字段值为空

## 问题原因分析

在`src/pages/myOrder/commentOrder.vue`文件中：

1. **缺少Policy和Signature生成逻辑**：
   - 第84-86行注释说明需要生成policy和signature，但实际没有实现
   - `ossConfig.policy`和`ossConfig.signature`字段始终为空字符串

2. **上传表单数据不完整**：
   - 第144-145行的formData中policy和signature字段为空
   - 导致阿里云OSS服务器收到的签名信息为空

## 修复方案

### 1. 安装加密依赖

```bash
pnpm add crypto-js
pnpm add -D @types/crypto-js
```

### 2. 创建OSS工具函数

新建文件：`src/utils/ossUtils.ts`

主要功能：
- `generateOSSPolicy()`: 生成符合阿里云OSS要求的Policy JSON并进行base64编码
- `generateOSSSignature()`: 使用HMAC-SHA1算法对Policy进行签名
- `generateOSSSignatureInfo()`: 生成完整的Policy和Signature信息
- `validateOSSConfig()`: 验证OSS配置是否完整

### 3. 修改评论页面上传逻辑

修改文件：`src/pages/myOrder/commentOrder.vue`

主要改动：
1. **导入OSS工具函数**：
   ```typescript
   import { generateOSSSignatureInfo, validateOSSConfig } from '@/utils/ossUtils'
   ```

2. **完善getOssToken函数**：
   - 在获取STS凭证后，立即生成Policy和Signature
   - 使用项目中定义的`UPLOAD_IMG_MAXSIZE`作为文件大小限制
   - 使用`commentImageUploadPath`作为文件路径前缀
   - 添加配置验证逻辑

3. **增强ossUpload函数**：
   - 在上传前验证OSS配置是否完整
   - 添加详细的日志输出便于调试
   - 改进错误处理逻辑

## 修复后的工作流程

1. **获取STS凭证**：调用`getStsTokenApi()`获取临时访问凭证
2. **生成签名信息**：使用`generateOSSSignatureInfo()`生成Policy和Signature
3. **验证配置**：使用`validateOSSConfig()`确保所有必需字段都已填充
4. **执行上传**：使用完整的formData进行文件上传

## 关键技术细节

### Policy生成
```json
{
  "expiration": "2024-12-31T23:59:59.000Z",
  "conditions": [
    ["content-length-range", 0, 3145728],
    ["eq", "$success_action_status", "200"],
    ["starts-with", "$key", "mall/comment/"]
  ]
}
```

### Signature计算
```
Signature = base64(hmac-sha1(AccessKeySecret, base64(policy)))
```

### 上传表单数据
```javascript
formData: {
  name: "文件名",
  key: "mall/comment/timestamp_random.jpg",
  OSSAccessKeyId: "STS临时AccessKeyId",
  success_action_status: "200",
  policy: "base64编码的Policy",
  signature: "HMAC-SHA1签名",
  "x-oss-security-token": "STS安全令牌"
}
```

## 验证方法

1. **检查控制台日志**：
   - 查看"OSS签名生成成功"日志
   - 确认Policy和Signature不为空

2. **监控上传请求**：
   - 检查formData中是否包含完整的签名信息
   - 验证Policy和Signature字段有值

3. **测试上传功能**：
   - 在评论页面选择图片上传
   - 观察是否能成功上传到OSS

## 注意事项

1. **安全性**：敏感信息（如AccessKeySecret）在日志中已做脱敏处理
2. **时效性**：Policy设置1小时过期时间，与STS凭证有效期匹配
3. **文件限制**：使用项目中定义的3MB文件大小限制
4. **路径规范**：使用统一的文件路径前缀`mall/comment/`

## 相关文件

- `src/utils/ossUtils.ts` - OSS工具函数（新增）
- `src/utils/ossUtils.example.ts` - 使用示例（新增）
- `src/pages/myOrder/commentOrder.vue` - 评论页面（修改）
- `package.json` - 依赖配置（更新）

修复完成后，阿里云OSS上传应该能够正常工作，不再出现"签名表单域为空"的错误。
