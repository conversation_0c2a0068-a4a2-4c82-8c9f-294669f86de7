// OSS工具函数使用示例
import { generateOSSSignatureInfo, validateOSSConfig } from './ossUtils'

// 示例：生成OSS签名信息
function exampleGenerateSignature() {
  const accessKeySecret = 'your-access-key-secret'
  
  const signatureInfo = generateOSSSignatureInfo(accessKeySecret, {
    expiration: 3600, // 1小时过期
    maxFileSize: 10 * 1024 * 1024, // 10MB
    keyPrefix: 'mall/comment/', // 文件路径前缀
    successActionStatus: '200'
  })
  
  console.log('生成的Policy:', signatureInfo.policy)
  console.log('生成的Signature:', signatureInfo.signature)
  
  return signatureInfo
}

// 示例：验证OSS配置
function exampleValidateConfig() {
  const ossConfig = {
    url: 'https://your-bucket.oss-region.aliyuncs.com',
    accessKeyId: 'your-access-key-id',
    accessKeySecret: 'your-access-key-secret',
    securityToken: 'your-security-token',
    policy: 'generated-policy',
    signature: 'generated-signature'
  }
  
  const isValid = validateOSSConfig(ossConfig)
  console.log('OSS配置是否有效:', isValid)
  
  return isValid
}

// 示例：完整的OSS上传配置流程
function exampleCompleteFlow() {
  // 1. 模拟从后端获取的STS凭证
  const stsCredentials = {
    accessKeyId: 'STS.example-access-key-id',
    accessKeySecret: 'example-access-key-secret',
    securityToken: 'example-security-token',
    expiration: '2024-12-31T23:59:59Z'
  }
  
  // 2. 生成Policy和Signature
  const signatureInfo = generateOSSSignatureInfo(stsCredentials.accessKeySecret, {
    expiration: 3600,
    maxFileSize: 3 * 1024 * 1024, // 3MB，符合项目中的UPLOAD_IMG_MAXSIZE
    keyPrefix: 'mall/comment/',
    successActionStatus: '200'
  })
  
  // 3. 构建完整的OSS配置
  const ossConfig = {
    url: 'https://your-bucket.oss-region.aliyuncs.com',
    accessKeyId: stsCredentials.accessKeyId,
    accessKeySecret: stsCredentials.accessKeySecret,
    securityToken: stsCredentials.securityToken,
    policy: signatureInfo.policy,
    signature: signatureInfo.signature
  }
  
  // 4. 验证配置
  const isValid = validateOSSConfig(ossConfig)
  
  console.log('完整OSS配置:', {
    ...ossConfig,
    accessKeySecret: '***', // 隐藏敏感信息
    policy: ossConfig.policy.substring(0, 50) + '...',
    signature: ossConfig.signature.substring(0, 20) + '...'
  })
  console.log('配置验证结果:', isValid)
  
  return { ossConfig, isValid }
}

// 导出示例函数供调试使用
export {
  exampleGenerateSignature,
  exampleValidateConfig,
  exampleCompleteFlow
}
